用中文回答。
我的sql：/home/<USER>/documents/projects/halal-admin/manifest/sql/haji_jadwal.sql
参考项目现有的结构和实现，必要时可以查看最近的git log.
我现在要给后台管理系统返回“朝觐”的“日程”，请帮我实现。
“朝觐”是haji, “日程”是jadwal。

大概需要这几个接口:
1. 返回日程说明，haji_jadwal_description
2. 新增/编辑/删除 日程说明，只能有一个（多种语言）说明。
3. 日程列表（id, 展示顺序，日程时间，已经配置了哪些语言， 日程标题）
4. 新增/编辑/删除 日程（展示顺序，日程时间，已经配置了哪些语言， 日程标题， 日程内容）


【新增日程】输入规则说明：

1、展示顺序，为文本输入框，必填，输入框默认提示文案：请输入展示顺序。

提示：前端展示顺序由此编号决定，请按顺序填写：1、2、3…

此处校展示验顺序是否重复，如有重复，提示；此编号已存在，请输入不同的顺序编号。

2、日程时间，为文本输入框，必填，输入框展示提示文案：1 Mei 2025（3 Dzulqa'dah 1446 H）内容可为时间点，也可为时间段

3、语言切换Tab，支持为描述输入多种语言版本的内容；

管理员可自由切换语言进行编辑；提交后存储每种语言对应的内容，供前端按用户语言显示。

①  当前所选语言高亮，切换语言后可编辑对应内容；

② 切换语言时展示当前语言的输入框内容；

4、日程标题为文本输入框，必填。

①  输入框默认提示文案：请输入标题。最多可输入100个字符，支持中英文、数字和特殊字符。

②  合规校验：输入框失去光标时，判断数据是否为空，为空提示：请输入标题；输入超出最大限制时，不允许输入。提示：最多支持100个字符。

5、内容为富文本编辑框组件。必填。







